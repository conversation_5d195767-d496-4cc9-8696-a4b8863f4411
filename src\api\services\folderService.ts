import { ApiClient } from '../core/apiClient';
import {
  FolderDto,
  FolderCreateData,
  FolderContentsOptions,
  FolderContentsResponse
} from '../types/interfaces';

export class FolderService {
  private client: ApiClient;
  
  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }
  
  /**
   * Create a new folder
   * @param folderData Folder creation data
   * @returns Information about the created folder
   */
  async create(folderData: FolderCreateData): Promise<FolderDto> {
    return this.client.post<FolderDto>('/folders', folderData);
  }
  
  /**
   * Get the contents of a folder (files and subfolders)
   * @param folderId ID of the folder
   * @param options Filtering and pagination options
   * @returns Folder contents with pagination
   */
  async getContents(folderId: string, options?: FolderContentsOptions): Promise<FolderContentsResponse> {
    return this.client.get<FolderContentsResponse>(`/folders/${folderId}/contents`, {
      params: options
    });
  }
  
  /**
   * Download folder as ZIP
   * @param folderId ID of the folder to download
   * @param includeSubfolders Whether to include subfolders in the ZIP
   * @param maxZipSize Maximum size of the ZIP file in bytes
   * @returns Blob containing the ZIP file
   */
  async download(
    folderId: string, 
    includeSubfolders: boolean = true, 
    maxZipSize?: number
  ): Promise<Blob> {
    return this.client.downloadFile(`/folders/${folderId}/download`, {
      params: {
        includeSubfolders,
        ...(maxZipSize ? { maxZipSize } : {})
      }
    });
  }
  
  /**
   * Delete a folder
   * @param folderId ID of the folder to delete
   * @param permanent Whether to permanently delete the folder
   */
  async delete(folderId: string, permanent: boolean = false): Promise<void> {
    await this.client.delete(`/folders/${folderId}`, {
      params: { permanent }
    });
  }
  
  /**
   * Update folder metadata
   * @param folderId ID of the folder
   * @param data Updated folder data
   * @returns Updated folder information
   */
  async update(
    folderId: string, 
    data: { name?: string; description?: string; parentFolderId?: string }
  ): Promise<FolderDto> {
    return this.client.put<FolderDto>(`/folders/${folderId}`, data);
  }
  
  /**
   * Move a folder to another parent folder
   * @param folderId ID of the folder to move
   * @param targetFolderId ID of the destination parent folder
   */
  async move(folderId: string, targetFolderId: string): Promise<void> {
    await this.client.post(`/folders/${folderId}/move`, { targetFolderId });
  }
} 