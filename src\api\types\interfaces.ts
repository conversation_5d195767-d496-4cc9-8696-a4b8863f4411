// File Management Interfaces
export interface FileDto {
  id: string;
  name: string;
  displayName?: string;
  fileSize: number;
  mimeType: string;
  filePath: string;
  hashMd5?: string;
  hashSha256?: string;
  storageProvider: string;
  externalId?: string;
  parentFolderId?: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  version: number;
  permissions: string[];
  isShared: boolean;
}

export interface UploadOptions {
  parentFolderId?: string;
  displayName?: string;
  description?: string;
  syncToGoogleDrive?: boolean;
  tags?: string[];
  overwriteExisting?: boolean;
  customMetadata?: Record<string, any>;
}

export interface MultiUploadOptions {
  parentFolderId?: string;
  syncToGoogleDrive?: boolean;
  failOnError?: boolean;
  tags?: string[];
}

export interface MultiUploadResponse {
  totalFiles: number;
  successfulUploads: number;
  failedUploads: number;
  uploadedFiles: FileDto[];
  errors: {
    fileName: string;
    errorMessage: string;
    errorCode: string;
  }[];
  totalProcessingTime: string;
  totalSizeUploaded: number;
}

export interface FileUpdateData {
  displayName?: string;
  description?: string;
  parentFolderId?: string;
  tags?: string[];
}

export interface FileCopyOptions {
  targetFolderId: string;
  newName?: string;
  syncToGoogleDrive?: boolean;
}

// Folder Management Interfaces
export interface FolderDto {
  id: string;
  name: string;
  parentFolderId?: string;
  ownerId: string;
  path: string;
  level: number;
  createdAt: string;
  updatedAt: string;
  fileCount: number;
  subfolderCount: number;
  permissions: string[];
}

export interface FolderCreateData {
  name: string;
  parentFolderId?: string;
  description?: string;
}

export interface FolderContentsOptions {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

export interface FolderContentsResponse {
  folders: FolderDto[];
  files: FileDto[];
  pagination: PaginationInfo;
}

// Permission Management Interfaces
export interface PermissionRequest {
  userId?: string;
  roleId?: string;
  permission: 'Read' | 'Write' | 'Delete' | 'Share' | 'Admin';
  expiresAt?: string;
}

export interface PermissionDto {
  id: string;
  userId: string;
  userName: string;
  permission: string;
  grantedAt: string;
  expiresAt?: string;
  grantedBy: string;
}

// Sharing Interfaces
export interface ShareOptions {
  shareType: 'Public' | 'Password' | 'UserSpecific';
  password?: string;
  expiresAt?: string;
  maxDownloads?: number;
}

export interface ShareDto {
  id: string;
  token: string;
  shareType: string;
  shareUrl: string;
  expiresAt?: string;
  maxDownloads?: number;
  currentDownloads: number;
  isActive: boolean;
  createdAt: string;
}

export interface ShareAccessResponse {
  file: Partial<FileDto>;
  shareInfo: {
    shareType: string;
    expiresAt?: string;
    remainingDownloads?: number;
  };
  downloadUrl: string;
}

// Sync Interfaces
export interface SyncOptions {
  fileId?: string;
  forceSync?: boolean;
}

export interface SyncJobDto {
  id: string;
  fileId: string;
  provider: string;
  status: 'Pending' | 'InProgress' | 'Completed' | 'Failed' | 'Cancelled';
  progress: number;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
  syncDirection: string;
}

export interface SyncStatusResponse {
  syncJobs: SyncJobDto[];
  overallStatus: string;
}

// Chunked Upload Interfaces
export interface ChunkedUploadOptions extends UploadOptions {
  chunkSize?: number;
  onProgress?: (progress: UploadProgress) => void;
}

export interface UploadProgress {
  totalChunks: number;
  uploadedChunks: number;
  progress: number;
  chunkNumber: number;
}

export interface ChunkedUploadInitRequest {
  fileName: string;
  totalFileSize: number;
  contentType: string;
  fileHash: string;
  parentFolderId?: string;
  displayName?: string;
  description?: string;
  syncToGoogleDrive?: boolean;
  tags?: string[];
}

export interface ChunkedUploadSession {
  sessionId: string;
  fileName: string;
  totalFileSize: number;
  chunkSize: number;
  totalChunks: number;
  expiresAt: string;
  uploadToken: string;
}

export interface ChunkUploadResult {
  chunkNumber: number;
  chunkSize: number;
  isLastChunk: boolean;
  chunkHash: string;
  remainingChunks: number;
  progressPercentage: number;
}

// Pagination
export interface PaginationInfo {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
  nextPage?: string;
  previousPage?: string;
}

// API Error Response
export interface ApiErrorResponse {
  type: string;
  title: string;
  status: number;
  detail: string;
  instance: string;
  errors?: Record<string, string[]>;
  traceId: string;
} 