# Tài Liệu API - <PERSON><PERSON> Thống Quản Lý File VeasyFileManager

## Tổng Quan

VeasyFileManager là hệ thống quản lý file enterprise với các tính năng:
- Quản lý file và thư mục với phân quyền chi tiết
- <PERSON><PERSON><PERSON> hợp Google Drive và Cloudflare R2
- Chia sẻ file/thư mục với nhiều cơ chế bảo mật
- Upload file đa dạng (single, multiple, chunked)
- Đồng bộ hóa với cloud storage

**Base URL**: `/api/v1`
**Authentication**: JWT Bearer <PERSON>ken (bắt buộc cho tất cả endpoint trừ share public)

---

## 1. Quản Lý File (File Management)

### 1.1 Upload File Đơn Lẻ

**Endpoint**: `POST /files/upload`
**Mô tả**: Upload một file duy nhất với metadata

#### Request
```http
Content-Type: multipart/form-data
Authorization: Bearer {token}
```

**Form Data**:
```javascript
{
  "file": File,                    // File cần upload (bắt buộc)
  "parentFolderId": "uuid",        // ID thư mục cha (tùy chọn)
  "displayName": "string",         // Tên hiển thị (tùy chọn)
  "description": "string",         // Mô tả file (tùy chọn)
  "syncToGoogleDrive": boolean,    // Đồng bộ lên Google Drive (mặc định: true)
  "tags": "tag1,tag2,tag3",       // Tags phân cách bởi dấu phẩy (tùy chọn)
  "overwriteExisting": boolean,    // Ghi đè file trùng tên (mặc định: false)
  "customMetadata": "json_string"  // Metadata bổ sung dạng JSON (tùy chọn)
}
```

#### Response Success (201 Created)
```json
{
  "id": "uuid",
  "name": "document.pdf",
  "displayName": "Tài liệu quan trọng",
  "fileSize": 1024000,
  "mimeType": "application/pdf",
  "filePath": "/storage/files/uuid/document.pdf",
  "hashMd5": "abc123...",
  "hashSha256": "def456...",
  "storageProvider": "CloudflareR2",
  "externalId": "gdrive_file_id",
  "parentFolderId": "uuid",
  "ownerId": "uuid",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "version": 1,
  "permissions": ["Read", "Write", "Delete"],
  "isShared": false
}
```

#### Error Cases
- **400 Bad Request**: File không hợp lệ, kích thước quá lớn
- **401 Unauthorized**: Token không hợp lệ
- **413 Payload Too Large**: File vượt quá giới hạn cho phép
- **409 Conflict**: File trùng tên (khi overwriteExisting=false)

#### Luồng Xử Lý
1. Validate authentication token
2. Kiểm tra quyền upload vào thư mục cha
3. Validate file (size, type, tên file)
4. Tính toán hash (MD5, SHA256)
5. Upload lên storage provider (R2/Google Drive)
6. Lưu metadata vào database
7. Trigger sync job (nếu cần)

---

### 1.2 Upload Multiple Files

**Endpoint**: `POST /files/upload/multiple`
**Mô tả**: Upload nhiều file cùng lúc

#### Request
```http
Content-Type: multipart/form-data
```

**Form Data**:
```javascript
{
  "files": File[],                 // Danh sách files (bắt buộc)
  "parentFolderId": "uuid",        // ID thư mục cha (tùy chọn)
  "syncToGoogleDrive": boolean,    // Đồng bộ lên Google Drive
  "failOnError": boolean,          // Dừng khi gặp lỗi (mặc định: false)
  "tags": "tag1,tag2"             // Tags chung cho tất cả files
}
```

#### Response Success (201 Created)
```json
{
  "totalFiles": 5,
  "successfulUploads": 4,
  "failedUploads": 1,
  "uploadedFiles": [
    {
      "id": "uuid",
      "name": "file1.pdf",
      // ... file dto fields
    }
  ],
  "errors": [
    {
      "fileName": "file5.exe",
      "errorMessage": "File type not allowed",
      "errorCode": "INVALID_FILE_TYPE"
    }
  ],
  "totalProcessingTime": "00:00:30",
  "totalSizeUploaded": 10485760
}
```

#### Error Cases
- **400 Bad Request**: Không có file nào được upload
- **401 Unauthorized**: Token không hợp lệ

---

### 1.3 Chunked Upload (Upload File Lớn)

Dành cho file lớn (>100MB), chia thành nhiều chunk để upload.

#### Bước 1: Khởi tạo session
**Endpoint**: `POST /files/upload/chunked/init`

**Request**:
```json
{
  "fileName": "large_video.mp4",
  "totalFileSize": 1073741824,
  "contentType": "video/mp4",
  "fileHash": "sha256_hash",
  "parentFolderId": "uuid",
  "displayName": "Video presentation",
  "description": "Presentation video",
  "syncToGoogleDrive": true,
  "tags": ["presentation", "video"]
}
```

**Response (201 Created)**:
```json
{
  "sessionId": "uuid",
  "fileName": "large_video.mp4",
  "totalFileSize": 1073741824,
  "chunkSize": 10485760,
  "totalChunks": 103,
  "expiresAt": "2024-01-01T01:00:00Z",
  "uploadToken": "secure_token"
}
```

#### Bước 2: Upload chunks
**Endpoint**: `POST /files/upload/chunked/{sessionId}/chunk`

**Request**:
```http
Content-Type: multipart/form-data
```

**Form Data**:
```javascript
{
  "chunk": File,           // Chunk data
  "chunkNumber": 1,        // Số thứ tự chunk (bắt đầu từ 1)
  "chunkHash": "hash"      // Hash của chunk để verify
}
```

**Response (200 OK)**:
```json
{
  "chunkNumber": 1,
  "chunkSize": 10485760,
  "isLastChunk": false,
  "chunkHash": "verified_hash",
  "remainingChunks": 102,
  "progressPercentage": 0.97
}
```

#### Bước 3: Hoàn thành upload
**Endpoint**: `POST /files/upload/chunked/{sessionId}/complete`

**Request**:
```json
{
  "chunkHashes": ["hash1", "hash2", "..."],
  "finalFileHash": "complete_file_sha256"
}
```

**Response (201 Created)**:
```json
{
  // FileDto của file đã hoàn thành
}
```

---

### 1.4 Download File

**Endpoint**: `GET /files/{id}/download`
**Mô tả**: Download file hoặc lấy presigned URL

#### Request
```http
GET /files/{id}/download?presigned=false&expiration=3600
Authorization: Bearer {token}
```

**Query Parameters**:
- `presigned` (boolean): Trả về presigned URL thay vì stream file
- `expiration` (int): Thời gian hết hạn URL (giây, mặc định: 3600)

#### Response (200 OK)

**Direct Download**:
```http
Content-Type: application/pdf
Content-Disposition: attachment; filename="document.pdf"
Content-Length: 1024000

[Binary file data]
```

**Presigned URL**:
```json
{
  "url": "https://storage.example.com/presigned-url",
  "expires": "2024-01-01T01:00:00Z"
}
```

#### Error Cases
- **401 Unauthorized**: Không có quyền truy cập
- **404 Not Found**: File không tồn tại hoặc đã bị xóa
- **403 Forbidden**: Không có quyền download

---

### 1.5 Cập Nhật File Metadata

**Endpoint**: `PUT /files/{id}`

#### Request
```json
{
  "displayName": "Tài liệu mới",
  "description": "Mô tả cập nhật",
  "parentFolderId": "new_folder_uuid",
  "tags": ["tag1", "tag2", "updated"]
}
```

#### Response (200 OK)
```json
{
  // FileDto với thông tin đã cập nhật
}
```

---

### 1.6 Xóa File

**Endpoint**: `DELETE /files/{id}?permanent=false`

**Query Parameters**:
- `permanent` (boolean): Xóa vĩnh viễn (mặc định: false - soft delete)

#### Response
- **204 No Content**: Xóa thành công
- **404 Not Found**: File không tồn tại

---

### 1.7 Copy File

**Endpoint**: `POST /files/{id}/copy`

#### Request
```json
{
  "targetFolderId": "uuid",
  "newName": "copy_of_document.pdf",
  "syncToGoogleDrive": true
}
```

#### Response (201 Created)
```json
{
  // FileDto của file copy mới
}
```

---

### 1.8 Move File

**Endpoint**: `POST /files/{id}/move`

#### Request
```json
{
  "targetFolderId": "uuid"
}
```

#### Response (204 No Content)

---

## 2. Quản Lý Thư Mục (Folder Management)

### 2.1 Tạo Thư Mục

**Endpoint**: `POST /folders`

#### Request
```json
{
  "name": "Tài liệu dự án",
  "parentFolderId": "uuid",
  "description": "Thư mục chứa tài liệu dự án ABC"
}
```

#### Response (201 Created)
```json
{
  "id": "uuid",
  "name": "Tài liệu dự án",
  "parentFolderId": "uuid",
  "ownerId": "uuid",
  "path": "/Projects/Tài liệu dự án",
  "level": 2,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "fileCount": 0,
  "subfolderCount": 0,
  "permissions": ["Read", "Write", "Delete", "Admin"]
}
```

#### Error Cases
- **409 Conflict**: Thư mục trùng tên trong cùng parent
- **403 Forbidden**: Không có quyền tạo thư mục trong parent

---

### 2.2 Lấy Nội Dung Thư Mục

**Endpoint**: `GET /folders/{id}/contents`

#### Request
```http
GET /folders/{id}/contents?page=1&pageSize=20&sortBy=Name&sortDirection=ASC
```

#### Response (200 OK)
```json
{
  "folders": [
    {
      "id": "uuid",
      "name": "Subfolder 1",
      // ... folder dto fields
    }
  ],
  "files": [
    {
      "id": "uuid",
      "name": "document.pdf",
      // ... file dto fields
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 45,
    "totalPages": 3,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

---

### 2.3 Download Thư Mục (ZIP)

**Endpoint**: `GET /folders/{id}/download`

#### Request
```http
GET /folders/{id}/download?includeSubfolders=true&maxZipSize=1073741824
```

**Query Parameters**:
- `includeSubfolders` (boolean): Bao gồm thư mục con
- `maxZipSize` (long): Kích thước tối đa của file ZIP (bytes)

#### Response (200 OK)
```http
Content-Type: application/zip
Content-Disposition: attachment; filename="folder_name.zip"

[ZIP file data]
```

#### Luồng Xử Lý
1. Kiểm tra quyền truy cập thư mục
2. Duyệt tree structure để thu thập files
3. Kiểm tra tổng kích thước < maxZipSize
4. Tạo ZIP stream với file content từ storage
5. Return ZIP file

---

## 3. Quản Lý Quyền (Permission Management)

### 3.1 Cấp Quyền File

**Endpoint**: `POST /files/{id}/permissions`

#### Request
```json
{
  "userId": "uuid",           // Cấp quyền cho user (tùy chọn)
  "roleId": "uuid",          // Cấp quyền cho role (tùy chọn)
  "permission": "Write",      // Read, Write, Delete, Share, Admin
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

#### Response (201 Created)
```json
"permission_uuid"  // ID của permission được tạo
```

### 3.2 Lấy Danh Sách Quyền

**Endpoint**: `GET /files/{id}/permissions`

#### Response (200 OK)
```json
[
  {
    "id": "uuid",
    "userId": "uuid",
    "userName": "Nguyễn Văn A",
    "permission": "Write",
    "grantedAt": "2024-01-01T00:00:00Z",
    "expiresAt": "2024-12-31T23:59:59Z",
    "grantedBy": "uuid"
  }
]
```

### 3.3 Thu Hồi Quyền

**Endpoint**: `DELETE /files/permissions/{permissionId}`

#### Response (204 No Content)

---

## 4. Chia Sẻ (File/Folder Sharing)

### 4.1 Tạo Link Chia Sẻ File

**Endpoint**: `POST /files/{id}/share`

#### Request
```json
{
  "shareType": "Password",      // Public, Password, UserSpecific
  "password": "secure123",      // Bắt buộc khi shareType = Password
  "expiresAt": "2024-12-31T23:59:59Z",
  "maxDownloads": 10           // Giới hạn số lần download (tùy chọn)
}
```

#### Response (201 Created)
```json
{
  "id": "uuid",
  "token": "secure_share_token",
  "shareType": "Password",
  "shareUrl": "https://app.example.com/shares/secure_share_token",
  "expiresAt": "2024-12-31T23:59:59Z",
  "maxDownloads": 10,
  "currentDownloads": 0,
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00Z"
}
```

### 4.2 Truy Cập File Chia Sẻ (Public)

**Endpoint**: `GET /shares/{token}?password=secure123`
**Authentication**: Không bắt buộc

#### Response (200 OK)
```json
{
  "file": {
    // FileDto (limited fields for security)
    "id": "uuid",
    "name": "document.pdf",
    "displayName": "Tài liệu công khai",
    "fileSize": 1024000,
    "mimeType": "application/pdf",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "shareInfo": {
    "shareType": "Password",
    "expiresAt": "2024-12-31T23:59:59Z",
    "remainingDownloads": 8
  },
  "downloadUrl": "https://app.example.com/shares/secure_share_token/download"
}
```

#### Error Cases
- **401 Unauthorized**: Sai password hoặc link yêu cầu đăng nhập
- **404 Not Found**: Token không hợp lệ hoặc đã hết hạn
- **429 Too Many Requests**: Đã đạt giới hạn download

---

## 5. Đồng Bộ Google Drive

### 5.1 Trigger Sync

**Endpoint**: `POST /sync/google-drive`

#### Request
```json
{
  "fileId": "uuid",        // Sync file cụ thể (tùy chọn)
  "forceSync": false       // Bắt buộc sync dù đã up-to-date
}
```

#### Response (202 Accepted)
```json
{
  "jobId": "uuid",
  "status": "Queued",
  "message": "Sync job đã được tạo và đang chờ xử lý"
}
```

### 5.2 Lấy Trạng Thái Sync

**Endpoint**: `GET /sync/status?fileId=uuid&provider=GoogleDrive`

#### Response (200 OK)
```json
{
  "syncJobs": [
    {
      "id": "uuid",
      "fileId": "uuid",
      "provider": "GoogleDrive",
      "status": "InProgress",
      "progress": 75.5,
      "startedAt": "2024-01-01T00:00:00Z",
      "completedAt": null,
      "errorMessage": null,
      "syncDirection": "ToCloud"
    }
  ],
  "overallStatus": "InProgress"
}
```

**Sync Status Values**:
- `Pending`: Chờ xử lý
- `InProgress`: Đang sync
- `Completed`: Hoàn thành
- `Failed`: Thất bại
- `Cancelled`: Đã hủy

---

## 6. Error Handling

### Standard Error Response Format
```json
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
  "title": "Bad Request",
  "status": 400,
  "detail": "Chi tiết lỗi cụ thể",
  "instance": "/api/v1/files/upload",
  "errors": {
    "file": ["File không được để trống"],
    "fileSize": ["File vượt quá 100MB"]
  },
  "traceId": "correlation-id"
}
```

### Common HTTP Status Codes

| Code | Meaning | Mô tả |
|------|---------|-------|
| 200 | OK | Request thành công |
| 201 | Created | Tạo resource thành công |
| 204 | No Content | Request thành công, không có response body |
| 400 | Bad Request | Request không hợp lệ |
| 401 | Unauthorized | Chưa đăng nhập hoặc token hết hạn |
| 403 | Forbidden | Không có quyền thực hiện thao tác |
| 404 | Not Found | Resource không tồn tại |
| 409 | Conflict | Xung đột dữ liệu (trùng tên, etc.) |
| 413 | Payload Too Large | File upload quá lớn |
| 429 | Too Many Requests | Quá giới hạn rate limit |
| 500 | Internal Server Error | Lỗi server |

### Business Logic Errors

#### File Upload Errors
- `FILE_TOO_LARGE`: File vượt quá kích thước cho phép
- `INVALID_FILE_TYPE`: Loại file không được phép
- `STORAGE_QUOTA_EXCEEDED`: Vượt quá quota lưu trữ
- `DUPLICATE_FILE_NAME`: Tên file trùng lặp
- `INVALID_PARENT_FOLDER`: Thư mục cha không hợp lệ

#### Permission Errors
- `INSUFFICIENT_PERMISSIONS`: Không đủ quyền
- `PERMISSION_ALREADY_EXISTS`: Quyền đã tồn tại
- `INVALID_PERMISSION_TYPE`: Loại quyền không hợp lệ
- `PERMISSION_EXPIRED`: Quyền đã hết hạn

#### Sharing Errors
- `SHARE_LIMIT_EXCEEDED`: Vượt quá số lần chia sẻ
- `INVALID_SHARE_PASSWORD`: Mật khẩu chia sẻ không đúng
- `SHARE_EXPIRED`: Link chia sẻ đã hết hạn
- `MAX_DOWNLOADS_REACHED`: Đã đạt giới hạn download

---

## 7. Rate Limiting

API có áp dụng rate limiting để bảo vệ hệ thống:

### Limits
- **File Upload**: 10 requests/minute per user
- **API Calls**: 1000 requests/hour per user
- **Share Access**: 100 requests/hour per IP

### Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
Retry-After: 60
```

---

## 8. Pagination

Các endpoint list sử dụng cursor-based pagination:

### Request
```http
GET /files?page=1&pageSize=20&sortBy=CreatedAt&sortDirection=DESC
```

### Response
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "totalItems": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrevious": false,
    "nextPage": "/files?page=2&pageSize=20",
    "previousPage": null
  }
}
```

---

## 9. Security Considerations

### Authentication
- JWT Bearer token với expiration time
- Refresh token mechanism
- Duende IdentityServer integration

### Authorization
- Role-based access control (RBAC)
- Resource-level permissions
- Inherited permissions từ parent folder

### File Security
- Virus scanning trước khi lưu trữ
- File type whitelist/blacklist
- Encrypted storage
- Secure presigned URLs với expiration

### Data Protection
- Sensitive data masking trong logs
- GDPR compliance
- Data retention policies
- Audit trails cho mọi thao tác

---

## 10. SDK và Integration

### Webhook Events
Hệ thống hỗ trợ webhook để notify các sự kiện:

```json
{
  "eventType": "file.uploaded",
  "timestamp": "2024-01-01T00:00:00Z",
  "data": {
    "fileId": "uuid",
    "userId": "uuid",
    "fileName": "document.pdf",
    "fileSize": 1024000
  },
  "signature": "webhook_signature"
}
```

### Event Types
- `file.uploaded`
- `file.downloaded`
- `file.deleted`
- `file.shared`
- `folder.created`
- `permission.granted`
- `sync.completed` 