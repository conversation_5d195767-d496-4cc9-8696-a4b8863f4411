# Cách Thức Ho<PERSON>t Động Của Chunked Upload

## Tổng Quan

Chunked upload là kỹ thuật chia file lớn thành nhiều phần nhỏ (chunks) để upload riêng lẻ, sau đó ghép lại thành file hoàn chỉnh. Điều này giúp upload file lớn ổn định và có thể phục hồi khi gặp lỗi.

---

## 1. Kiến Trúc Tổng Thể

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Frontend  │    │   API       │    │  Database   │    │  Storage    │
│             │    │             │    │             │    │             │
│ File Reader │───▶│ Session Mgr │───▶│ Metadata    │    │ File Chunks │
│ Chunk Logic │    │ Chunk Recv  │    │ Session     │    │ Temp Store  │
│ Progress UI │    │ File Merger │    │ Progress    │    │ Final File  │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### Các Thành Phần Chính:

1. **Frontend**: Chia file, quản lý upload
2. **API**: Nhận chunks, quản lý session
3. **Database**: Lưu metadata, tracking progress  
4. **Storage**: Lưu trữ chunks và file cuối cùng

---

## 2. Luồng Hoạt Động Chi Tiết

### Phase 1: Khởi Tạo Session

```javascript
// 1. Frontend chuẩn bị file
const file = selectedFile; // File object từ input
const fileSize = file.size;
const chunkSize = 10 * 1024 * 1024; // 10MB
const totalChunks = Math.ceil(fileSize / chunkSize);

// 2. Tính toán hash file (optional)
const fileHash = await calculateSHA256(file);

// 3. Gửi request khởi tạo
const initRequest = {
    fileName: file.name,
    totalFileSize: fileSize,
    contentType: file.type,
    fileHash: fileHash,
    parentFolderId: folderId,
    // ... metadata khác
};
```

**Backend xử lý:**
```csharp
// 1. Validate request
if (request.TotalFileSize > MaxFileSize) 
    throw new ValidationException("File too large");

// 2. Tạo session
var session = new UploadSession {
    Id = Guid.NewGuid(),
    FileName = request.FileName,
    TotalFileSize = request.TotalFileSize,
    ChunkSize = CalculateOptimalChunkSize(request.TotalFileSize),
    TotalChunks = Math.Ceiling(request.TotalFileSize / chunkSize),
    ExpiresAt = DateTime.UtcNow.AddHours(2),
    UserId = currentUser.Id,
    Status = UploadStatus.Initialized
};

// 3. Lưu vào database
await _sessionRepository.CreateAsync(session);

// 4. Tạo thư mục tạm cho chunks
await _storageService.CreateTempDirectory(session.Id);
```

### Phase 2: Upload Chunks

```javascript
// Frontend chia file thành chunks
for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, fileSize);
    
    // Tạo chunk
    const chunk = file.slice(start, end);
    const chunkNumber = i + 1;
    const chunkHash = await calculateSHA256(chunk);
    
    // Upload chunk
    await uploadChunk(sessionId, chunk, chunkNumber, chunkHash);
}

async function uploadChunk(sessionId, chunk, chunkNumber, chunkHash) {
    const formData = new FormData();
    formData.append('chunk', chunk);
    formData.append('chunkNumber', chunkNumber);
    formData.append('chunkHash', chunkHash);
    
    const response = await fetch(`/api/v1/files/upload/chunked/${sessionId}/chunk`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        },
        body: formData
    });
    
    return await response.json();
}
```

**Backend xử lý từng chunk:**
```csharp
public async Task<ChunkUploadResult> UploadChunkAsync(
    Guid sessionId, 
    IFormFile chunk, 
    int chunkNumber, 
    string chunkHash)
{
    // 1. Validate session
    var session = await _sessionRepository.GetByIdAsync(sessionId);
    if (session == null || session.IsExpired) 
        throw new NotFoundException("Session not found or expired");
    
    // 2. Validate chunk
    if (chunkNumber > session.TotalChunks)
        throw new ValidationException("Invalid chunk number");
    
    // 3. Verify chunk hash
    var actualHash = await CalculateHashAsync(chunk.OpenReadStream());
    if (actualHash != chunkHash)
        throw new ValidationException("Chunk hash mismatch");
    
    // 4. Lưu chunk vào storage
    var chunkPath = $"temp/{sessionId}/chunk_{chunkNumber:D4}";
    await _storageService.SaveChunkAsync(chunkPath, chunk.OpenReadStream());
    
    // 5. Update session progress
    session.UploadedChunks.Add(chunkNumber);
    session.UploadedBytes += chunk.Length;
    await _sessionRepository.UpdateAsync(session);
    
    // 6. Return progress
    return new ChunkUploadResult {
        ChunkNumber = chunkNumber,
        ChunkSize = chunk.Length,
        IsLastChunk = chunkNumber == session.TotalChunks,
        RemainingChunks = session.TotalChunks - session.UploadedChunks.Count,
        ProgressPercentage = (double)session.UploadedChunks.Count / session.TotalChunks * 100
    };
}
```

### Phase 3: Hoàn Thành Upload

```javascript
// Frontend hoàn thành upload
const completeRequest = {
    chunkHashes: allChunkHashes, // Array của tất cả chunk hashes
    finalFileHash: originalFileHash // Hash của file gốc
};

const response = await fetch(`/api/v1/files/upload/chunked/${sessionId}/complete`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(completeRequest)
});

const finalFile = await response.json();
```

**Backend merge chunks:**
```csharp
public async Task<FileDto> CompleteUploadAsync(
    Guid sessionId, 
    CompleteChunkedUploadRequest request)
{
    // 1. Validate session
    var session = await _sessionRepository.GetByIdAsync(sessionId);
    if (session.UploadedChunks.Count != session.TotalChunks)
        throw new ValidationException("Not all chunks uploaded");
    
    // 2. Verify chunk integrity
    await VerifyChunkIntegrityAsync(session, request.ChunkHashes);
    
    // 3. Merge chunks thành file cuối cùng
    var finalFilePath = await MergeChunksAsync(session);
    
    // 4. Verify final file hash
    var actualFileHash = await CalculateFileHashAsync(finalFilePath);
    if (request.FinalFileHash != null && actualFileHash != request.FinalFileHash)
        throw new ValidationException("Final file hash mismatch");
    
    // 5. Tạo File entity
    var file = new File {
        Id = Guid.NewGuid(),
        Name = session.FileName,
        DisplayName = session.DisplayName ?? session.FileName,
        FileSize = session.TotalFileSize,
        MimeType = session.ContentType,
        FilePath = finalFilePath,
        HashSha256 = actualFileHash,
        StorageProvider = StorageProvider.CloudflareR2,
        ParentFolderId = session.ParentFolderId,
        OwnerId = session.UserId,
        CreatedAt = DateTime.UtcNow,
        UpdatedAt = DateTime.UtcNow
    };
    
    // 6. Lưu vào database
    await _fileRepository.CreateAsync(file);
    
    // 7. Cleanup temp chunks
    await CleanupTempChunksAsync(session);
    
    // 8. Delete session
    await _sessionRepository.DeleteAsync(sessionId);
    
    // 9. Trigger background sync (if needed)
    if (session.SyncToGoogleDrive) {
        await _mediator.Send(new TriggerGoogleDriveSyncCommand { FileId = file.Id });
    }
    
    return _mapper.Map<FileDto>(file);
}
```

---

## 3. Quản Lý Memory và Performance

### 3.1 Streaming Chunks

```csharp
// Không load toàn bộ chunk vào memory
public async Task SaveChunkAsync(string path, Stream chunkStream)
{
    using var fileStream = new FileStream(path, FileMode.Create);
    await chunkStream.CopyToAsync(fileStream, bufferSize: 81920); // 80KB buffer
}

// Merge chunks bằng streaming
private async Task<string> MergeChunksAsync(UploadSession session)
{
    var finalFilePath = GenerateFinalFilePath(session);
    
    using var outputStream = new FileStream(finalFilePath, FileMode.Create);
    
    for (int i = 1; i <= session.TotalChunks; i++)
    {
        var chunkPath = $"temp/{session.Id}/chunk_{i:D4}";
        using var chunkStream = new FileStream(chunkPath, FileMode.Open);
        await chunkStream.CopyToAsync(outputStream);
    }
    
    return finalFilePath;
}
```

### 3.2 Parallel Processing

```javascript
// Frontend upload chunks song song
class ParallelChunkedUploader {
    constructor(options) {
        this.maxConcurrency = options.maxConcurrency || 3;
        this.activeUploads = 0;
        this.uploadQueue = [];
    }
    
    async uploadChunks(chunks, session) {
        return new Promise((resolve, reject) => {
            let completedChunks = 0;
            const results = [];
            
            // Add all chunks to queue
            chunks.forEach(chunk => {
                this.uploadQueue.push(async () => {
                    try {
                        const result = await this.uploadSingleChunk(chunk, session);
                        results[chunk.number - 1] = result;
                        completedChunks++;
                        
                        if (completedChunks === chunks.length) {
                            resolve(results);
                        }
                        
                        // Start next upload in queue
                        this.processQueue();
                        
                    } catch (error) {
                        reject(error);
                    }
                });
            });
            
            // Start initial uploads
            this.processQueue();
        });
    }
    
    processQueue() {
        while (this.activeUploads < this.maxConcurrency && this.uploadQueue.length > 0) {
            this.activeUploads++;
            const uploadTask = this.uploadQueue.shift();
            
            uploadTask().finally(() => {
                this.activeUploads--;
            });
        }
    }
}
```

---

## 4. Error Handling và Recovery

### 4.1 Retry Logic

```javascript
class ResilientChunkedUploader {
    async uploadSingleChunk(chunk, session, retryCount = 0) {
        try {
            return await this.doUploadChunk(chunk, session);
        } catch (error) {
            if (retryCount < this.maxRetries && this.isRetryableError(error)) {
                console.log(`Retrying chunk ${chunk.number}, attempt ${retryCount + 1}`);
                
                // Exponential backoff
                const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);
                await this.sleep(delay);
                
                return this.uploadSingleChunk(chunk, session, retryCount + 1);
            }
            throw error;
        }
    }
    
    isRetryableError(error) {
        // Network errors
        if (error.name === 'TypeError' && error.message.includes('fetch')) return true;
        
        // Timeout errors
        if (error.name === 'AbortError') return true;
        
        // Server errors (5xx)
        if (error.status >= 500 && error.status < 600) return true;
        
        // Rate limiting
        if (error.status === 429) return true;
        
        return false;
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

### 4.2 Resume Upload

```javascript
class ResumableChunkedUploader {
    constructor(options) {
        super(options);
        this.storageKey = 'chunked_upload_sessions';
    }
    
    async uploadFile(file, metadata) {
        // Check for existing session
        const savedSession = this.findSavedSession(file);
        
        if (savedSession && !this.isSessionExpired(savedSession)) {
            console.log('Resuming previous upload session');
            return this.resumeUpload(file, savedSession);
        }
        
        // Start new upload
        return super.uploadFile(file, metadata);
    }
    
    async resumeUpload(file, savedSession) {
        // Get server session status
        const serverSession = await this.getSessionStatus(savedSession.sessionId);
        
        if (!serverSession) {
            // Session expired on server, start fresh
            this.clearSavedSession(savedSession.sessionId);
            return this.uploadFile(file, savedSession.metadata);
        }
        
        // Resume from where we left off
        const remainingChunks = this.calculateRemainingChunks(file, serverSession);
        await this.uploadChunks(remainingChunks, serverSession);
        
        return this.completeUpload(serverSession);
    }
    
    saveSession(session, metadata) {
        const sessions = this.getSavedSessions();
        sessions[session.sessionId] = {
            ...session,
            metadata,
            savedAt: new Date().toISOString(),
            fileSignature: this.calculateFileSignature(file) // để verify file không đổi
        };
        localStorage.setItem(this.storageKey, JSON.stringify(sessions));
    }
}
```

---

## 5. Monitoring và Debugging

### 5.1 Progress Tracking

```javascript
class MonitoredChunkedUploader {
    constructor(options) {
        super(options);
        this.startTime = null;
        this.uploadedBytes = 0;
        this.metrics = {
            chunks: [],
            errors: [],
            retries: 0
        };
    }
    
    async uploadSingleChunk(chunk, session) {
        const chunkStartTime = performance.now();
        
        try {
            const result = await super.uploadSingleChunk(chunk, session);
            
            // Track successful chunk
            const chunkEndTime = performance.now();
            const chunkDuration = chunkEndTime - chunkStartTime;
            
            this.metrics.chunks.push({
                number: chunk.number,
                size: chunk.size,
                duration: chunkDuration,
                speed: chunk.size / (chunkDuration / 1000) // bytes per second
            });
            
            this.uploadedBytes += chunk.size;
            
            // Calculate overall progress
            const overallProgress = {
                percentage: (this.uploadedBytes / session.totalFileSize) * 100,
                uploadedBytes: this.uploadedBytes,
                totalBytes: session.totalFileSize,
                estimatedTimeRemaining: this.calculateETA(),
                currentSpeed: this.calculateCurrentSpeed()
            };
            
            this.onProgress(overallProgress);
            
            return result;
            
        } catch (error) {
            // Track failed chunk
            this.metrics.errors.push({
                chunkNumber: chunk.number,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            throw error;
        }
    }
    
    calculateETA() {
        if (this.metrics.chunks.length === 0) return null;
        
        const avgSpeed = this.calculateAverageSpeed();
        const remainingBytes = session.totalFileSize - this.uploadedBytes;
        
        return remainingBytes / avgSpeed; // seconds
    }
    
    calculateCurrentSpeed() {
        const recentChunks = this.metrics.chunks.slice(-3); // Last 3 chunks
        if (recentChunks.length === 0) return 0;
        
        const totalBytes = recentChunks.reduce((sum, chunk) => sum + chunk.size, 0);
        const totalTime = recentChunks.reduce((sum, chunk) => sum + chunk.duration, 0);
        
        return totalBytes / (totalTime / 1000); // bytes per second
    }
}
```

### 5.2 Backend Monitoring

```csharp
public class ChunkedUploadService
{
    private readonly ILogger<ChunkedUploadService> _logger;
    private readonly IMetricsCollector _metrics;
    
    public async Task<ChunkUploadResult> UploadChunkAsync(/* params */)
    {
        using var activity = _metrics.StartActivity("chunk_upload");
        activity.SetTag("session_id", sessionId);
        activity.SetTag("chunk_number", chunkNumber);
        
        var stopwatch = Stopwatch.StartNew();
        
        try 
        {
            var result = await DoUploadChunkAsync(/* params */);
            
            stopwatch.Stop();
            
            // Log metrics
            _logger.LogInformation(
                "Chunk uploaded successfully. SessionId: {SessionId}, ChunkNumber: {ChunkNumber}, " +
                "ChunkSize: {ChunkSize}, Duration: {Duration}ms",
                sessionId, chunkNumber, chunk.Length, stopwatch.ElapsedMilliseconds);
            
            _metrics.Counter("chunks_uploaded_total").Increment();
            _metrics.Histogram("chunk_upload_duration").Record(stopwatch.ElapsedMilliseconds);
            _metrics.Histogram("chunk_size_bytes").Record(chunk.Length);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, 
                "Chunk upload failed. SessionId: {SessionId}, ChunkNumber: {ChunkNumber}",
                sessionId, chunkNumber);
            
            _metrics.Counter("chunks_failed_total").Increment();
            
            throw;
        }
    }
}
```

---

## 6. Security Considerations

### 6.1 Validation và Authentication

```csharp
public async Task<ChunkUploadResult> UploadChunkAsync(/* params */)
{
    // 1. Authentication
    var user = await _userService.GetCurrentUserAsync();
    if (user == null) throw new UnauthorizedException();
    
    // 2. Session ownership validation
    var session = await _sessionRepository.GetByIdAsync(sessionId);
    if (session.UserId != user.Id) 
        throw new ForbiddenException("Session does not belong to user");
    
    // 3. Rate limiting
    await _rateLimitService.CheckLimitAsync(user.Id, "chunk_upload");
    
    // 4. File type validation
    if (!_fileTypeValidator.IsAllowed(session.ContentType))
        throw new ValidationException("File type not allowed");
    
    // 5. Chunk size validation
    if (chunk.Length > MaxChunkSize)
        throw new ValidationException("Chunk too large");
    
    // 6. Session expiration
    if (session.IsExpired)
        throw new ValidationException("Upload session expired");
    
    // 7. Virus scanning (async)
    _ = Task.Run(async () => {
        await _virusScanService.ScanChunkAsync(chunkPath);
    });
    
    // Continue with upload...
}
```

### 6.2 Cleanup và Resource Management

```csharp
public class ChunkedUploadCleanupService : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // Cleanup expired sessions every hour
        _timer = new Timer(CleanupExpiredSessions, null, TimeSpan.Zero, TimeSpan.FromHours(1));
    }
    
    private async void CleanupExpiredSessions(object state)
    {
        try
        {
            var expiredSessions = await _sessionRepository.GetExpiredSessionsAsync();
            
            foreach (var session in expiredSessions)
            {
                // Delete temp chunks
                await _storageService.DeleteDirectoryAsync($"temp/{session.Id}");
                
                // Delete session record
                await _sessionRepository.DeleteAsync(session.Id);
                
                _logger.LogInformation("Cleaned up expired session: {SessionId}", session.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cleanup of expired sessions");
        }
    }
}
```

---

## 7. Optimization Strategies

### 7.1 Adaptive Chunk Size

```javascript
class AdaptiveChunkedUploader {
    constructor(options) {
        super(options);
        this.connectionSpeed = null;
        this.adaptiveChunkSize = options.chunkSize;
    }
    
    async uploadFile(file, metadata) {
        // Detect connection speed
        this.connectionSpeed = await this.detectConnectionSpeed();
        
        // Adjust chunk size based on connection
        this.adaptiveChunkSize = this.calculateOptimalChunkSize();
        
        return super.uploadFile(file, metadata);
    }
    
    async detectConnectionSpeed() {
        const testSize = 100 * 1024; // 100KB test
        const testData = new ArrayBuffer(testSize);
        
        const startTime = performance.now();
        
        try {
            await fetch('/api/health-check', {
                method: 'POST',
                body: testData
            });
            
            const endTime = performance.now();
            const duration = (endTime - startTime) / 1000; // seconds
            const speed = testSize / duration; // bytes per second
            
            return speed;
        } catch {
            return null; // Use default chunk size
        }
    }
    
    calculateOptimalChunkSize() {
        if (!this.connectionSpeed) return this.chunkSize;
        
        const mbps = (this.connectionSpeed * 8) / (1024 * 1024);
        
        if (mbps > 50) return 20 * 1024 * 1024; // 20MB for fast connections
        if (mbps > 10) return 10 * 1024 * 1024; // 10MB for medium connections
        if (mbps > 1) return 5 * 1024 * 1024;   // 5MB for slow connections
        
        return 1 * 1024 * 1024; // 1MB for very slow connections
    }
}
```

### 7.2 Compression

```csharp
public class CompressedChunkedUploadService
{
    public async Task<ChunkUploadResult> UploadChunkAsync(/* params */)
    {
        // Decompress chunk if compressed
        Stream chunkStream = chunk.OpenReadStream();
        
        if (IsCompressed(chunk))
        {
            chunkStream = new GZipStream(chunkStream, CompressionMode.Decompress);
        }
        
        // Continue with normal processing
        var chunkPath = $"temp/{sessionId}/chunk_{chunkNumber:D4}";
        await _storageService.SaveChunkAsync(chunkPath, chunkStream);
        
        // ...
    }
    
    private bool IsCompressed(IFormFile chunk)
    {
        return chunk.Headers.ContainsKey("Content-Encoding") && 
               chunk.Headers["Content-Encoding"].Contains("gzip");
    }
}
```

---

## 8. Best Practices Summary

### Frontend:
1. **Chunk Size**: 5-10MB cho most cases, điều chỉnh theo connection speed
2. **Parallel Uploads**: Limit 2-3 concurrent chunks để avoid overwhelming
3. **Progress UI**: Update smooth nhưng không quá frequent (throttle)
4. **Error Recovery**: Implement retry với exponential backoff
5. **Memory Management**: Release chunk references sau khi upload
6. **Session Persistence**: Save session info để có thể resume

### Backend:
1. **Streaming**: Không load chunks vào memory, dùng streaming
2. **Validation**: Validate mọi thứ - auth, file type, chunk integrity
3. **Cleanup**: Auto cleanup expired sessions và temp files
4. **Monitoring**: Log metrics cho performance tuning
5. **Security**: Rate limiting, virus scanning, resource limits
6. **Scalability**: Design cho horizontal scaling

### Database:
1. **Indexing**: Index trên session_id, user_id, created_at
2. **Partitioning**: Partition session table theo date nếu volume lớn
3. **Cleanup**: Automated cleanup cho old sessions
4. **Backup**: Backup metadata, không cần backup temp chunks

Chunked upload là technique phức tạp nhưng rất hiệu quả cho large file uploads. Việc implement đúng cách sẽ mang lại user experience tốt và system reliability cao. 