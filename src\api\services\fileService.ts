import { ApiClient } from '../core/apiClient';
import {
  FileDto,
  UploadOptions,
  MultiUploadOptions,
  MultiUploadResponse,
  FileUpdateData,
  FileCopyOptions,
  PermissionRequest,
  PermissionDto,
  ShareOptions,
  ShareDto,
  ShareAccessResponse
} from '../types/interfaces';

export class FileService {
  private client: ApiClient;
  
  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }
  
  /**
   * Upload a single file
   * @param file File to upload
   * @param options Upload options
   * @returns Uploaded file information
   */
  async upload(file: File, options?: UploadOptions): Promise<FileDto> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (options?.parentFolderId) formData.append('parentFolderId', options.parentFolderId);
    if (options?.displayName) formData.append('displayName', options.displayName);
    if (options?.description) formData.append('description', options.description);
    if (options?.syncToGoogleDrive !== undefined) formData.append('syncToGoogleDrive', String(options.syncToGoogleDrive));
    if (options?.tags) formData.append('tags', options.tags.join(','));
    if (options?.overwriteExisting !== undefined) formData.append('overwriteExisting', String(options.overwriteExisting));
    if (options?.customMetadata) formData.append('customMetadata', JSON.stringify(options.customMetadata));
    
    return this.client.post<FileDto>('/files/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }
  
  /**
   * Upload multiple files
   * @param files Array of files to upload
   * @param options Upload options
   * @returns Information about the upload operation
   */
  async uploadMultiple(files: File[], options?: MultiUploadOptions): Promise<MultiUploadResponse> {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });
    
    if (options?.parentFolderId) formData.append('parentFolderId', options.parentFolderId);
    if (options?.syncToGoogleDrive !== undefined) formData.append('syncToGoogleDrive', String(options.syncToGoogleDrive));
    if (options?.failOnError !== undefined) formData.append('failOnError', String(options.failOnError));
    if (options?.tags) formData.append('tags', options.tags.join(','));
    
    return this.client.post<MultiUploadResponse>('/files/upload/multiple', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }
  
  /**
   * Get presigned download URL for a file
   * @param fileId ID of the file
   * @param expiration URL expiration time in seconds
   * @returns Object containing the presigned URL and expiry time
   */
  async getDownloadUrl(fileId: string, expiration: number = 3600): Promise<{ url: string, expires: string }> {
    return this.client.get<{ url: string, expires: string }>(`/files/${fileId}/download`, {
      params: {
        presigned: true,
        expiration
      }
    });
  }
  
  /**
   * Download a file directly
   * @param fileId ID of the file to download
   * @returns Blob containing the file data
   */
  async download(fileId: string): Promise<Blob> {
    return this.client.downloadFile(`/files/${fileId}/download`);
  }
  
  /**
   * Update file metadata
   * @param fileId ID of the file to update
   * @param data Updated metadata
   * @returns Updated file information
   */
  async updateMetadata(fileId: string, data: FileUpdateData): Promise<FileDto> {
    return this.client.put<FileDto>(`/files/${fileId}`, data);
  }
  
  /**
   * Delete a file
   * @param fileId ID of the file to delete
   * @param permanent Whether to permanently delete the file
   */
  async delete(fileId: string, permanent: boolean = false): Promise<void> {
    await this.client.delete(`/files/${fileId}`, {
      params: { permanent }
    });
  }
  
  /**
   * Copy a file
   * @param fileId ID of the file to copy
   * @param options Copy options
   * @returns Information about the copied file
   */
  async copy(fileId: string, options: FileCopyOptions): Promise<FileDto> {
    return this.client.post<FileDto>(`/files/${fileId}/copy`, options);
  }
  
  /**
   * Move a file to another folder
   * @param fileId ID of the file to move
   * @param targetFolderId ID of the destination folder
   */
  async move(fileId: string, targetFolderId: string): Promise<void> {
    await this.client.post(`/files/${fileId}/move`, { targetFolderId });
  }
  
  // Permission management
  
  /**
   * Grant permission for a file
   * @param fileId ID of the file
   * @param permission Permission details
   * @returns ID of the created permission
   */
  async grantPermission(fileId: string, permission: PermissionRequest): Promise<string> {
    return this.client.post<string>(`/files/${fileId}/permissions`, permission);
  }
  
  /**
   * List permissions for a file
   * @param fileId ID of the file
   * @returns List of permissions
   */
  async listPermissions(fileId: string): Promise<PermissionDto[]> {
    return this.client.get<PermissionDto[]>(`/files/${fileId}/permissions`);
  }
  
  /**
   * Revoke a permission
   * @param permissionId ID of the permission to revoke
   */
  async revokePermission(permissionId: string): Promise<void> {
    await this.client.delete(`/files/permissions/${permissionId}`);
  }
  
  // Sharing
  
  /**
   * Create a share link for a file
   * @param fileId ID of the file to share
   * @param options Share options
   * @returns Information about the created share
   */
  async createShareLink(fileId: string, options: ShareOptions): Promise<ShareDto> {
    return this.client.post<ShareDto>(`/files/${fileId}/share`, options);
  }
  
  /**
   * Access a shared file (public endpoint)
   * @param token Share token
   * @param password Password (for password-protected shares)
   * @returns Information about the shared file
   */
  async accessSharedFile(token: string, password?: string): Promise<ShareAccessResponse> {
    return this.client.get<ShareAccessResponse>(`/shares/${token}`, {
      params: password ? { password } : undefined
    });
  }
} 