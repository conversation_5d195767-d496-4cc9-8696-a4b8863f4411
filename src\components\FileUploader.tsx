import React, { useState, useRef, useEffect } from "react";
import VeasyFileManagerAPI from "../api";
import { useFileUpload } from "../api/hooks/useFileUpload";
import { FileDto, UploadOptions } from "../api/types/interfaces";

interface FileUploaderProps {
  api: VeasyFileManagerAPI;
  onUploadComplete?: (files: FileDto[]) => void;
  onUploadError?: (error: Error) => void;
  parentFolderId?: string;
  acceptedFileTypes?: string;
  maxFileSize?: number; // In bytes
  maxFiles?: number;
  multiple?: boolean;
  className?: string;
  buttonText?: string;
  showFileList?: boolean;
  autoUpload?: boolean;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  api,
  onUploadComplete,
  onUploadError,
  parentFolderId,
  acceptedFileTypes,
  maxFileSize,
  maxFiles = 10,
  multiple = false,
  className = "",
  buttonText = "Select Files",
  showFileList = true,
  autoUpload = true,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<FileDto[]>([]);
  const [errors, setErrors] = useState<{ fileName: string; message: string }[]>(
    []
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Use our custom upload hook
  const {
    upload,
    uploadMultiple,
    chunkedUpload,
    progress,
    isUploading,
    error,
    cancelUpload,
  } = useFileUpload(api, {
    onUploadComplete: (file) => {
      setUploadedFiles((prev) => [...prev, file]);
      onUploadComplete?.([...uploadedFiles, file]);
    },
    onUploadError,
    autoChunkedThreshold: 100 * 1024 * 1024, // 100MB
  });

  // Automatically upload files when selected if autoUpload is true
  useEffect(() => {
    if (autoUpload && selectedFiles.length > 0 && !isUploading) {
      handleUpload();
    }
  }, [selectedFiles, autoUpload, isUploading]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = event.target.files;
    if (!fileList) return;

    const newFiles: File[] = [];
    const newErrors: { fileName: string; message: string }[] = [];

    // Process each selected file
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];

      // Validate file size
      if (maxFileSize && file.size > maxFileSize) {
        newErrors.push({
          fileName: file.name,
          message: `File size exceeds maximum allowed (${(
            maxFileSize /
            (1024 * 1024)
          ).toFixed(2)} MB)`,
        });
        continue;
      }

      // Validate file type if acceptedFileTypes is provided
      if (acceptedFileTypes) {
        const fileTypes = acceptedFileTypes.split(",");
        const fileExtension = file.name.split(".").pop()?.toLowerCase();
        const mimeType = file.type;

        const isValidType = fileTypes.some((type) => {
          if (type.startsWith(".")) {
            // Extension check
            return `.${fileExtension}` === type;
          } else {
            // MIME type check
            return mimeType.match(new RegExp(type.replace("*", ".*")));
          }
        });

        if (!isValidType) {
          newErrors.push({
            fileName: file.name,
            message: "File type not allowed",
          });
          continue;
        }
      }

      // Add valid file to list
      newFiles.push(file);
    }

    // Check maximum number of files
    if (selectedFiles.length + newFiles.length > maxFiles) {
      newErrors.push({
        fileName: "Multiple files",
        message: `Cannot upload more than ${maxFiles} files at once`,
      });

      // Only take the first N files that would bring us to the max
      const allowedNewFiles = newFiles.slice(
        0,
        maxFiles - selectedFiles.length
      );
      setSelectedFiles((prev) => [...prev, ...allowedNewFiles]);
    } else {
      setSelectedFiles((prev) => [...prev, ...newFiles]);
    }

    // Set any validation errors
    if (newErrors.length > 0) {
      setErrors((prev) => [...prev, ...newErrors]);
    }

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle manual upload button click
  const handleUpload = async () => {
    if (selectedFiles.length === 0 || isUploading) return;

    setErrors([]);

    try {
      const uploadOptions: UploadOptions = {
        parentFolderId,
        syncToGoogleDrive: true,
      };

      let results: FileDto[];

      if (selectedFiles.length === 1) {
        
        // Single file upload
        const file = selectedFiles[0];
        const result = await upload(file, uploadOptions);
        results = [result];
      } else {
        // Multiple files upload
        results = await uploadMultiple(selectedFiles, uploadOptions);
      }

      setUploadedFiles((prev) => [...prev, ...results]);
      setSelectedFiles([]);
      onUploadComplete?.(results);
    } catch (err) {
      const error = err as Error;
      setErrors([{ fileName: "Upload Error", message: error.message }]);
      onUploadError?.(error);
    }
  };

  // Handle file removal from the list
  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
    if (bytes < 1024 * 1024 * 1024)
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  };

  return (
    <div className={`file-uploader ${className}`}>
      <div className="file-uploader__input">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileSelect}
          accept={acceptedFileTypes}
          multiple={multiple}
          disabled={isUploading}
          className="file-uploader__input-field"
          id="file-upload-input"
          aria-label="File upload input"
          tabIndex={0}
        />
        <label htmlFor="file-upload-input" className="file-uploader__button">
          {buttonText}
        </label>

        {!autoUpload && selectedFiles.length > 0 && (
          <button
            onClick={handleUpload}
            disabled={isUploading}
            className="file-uploader__upload-button"
            aria-label="Upload files"
            tabIndex={0}
          >
            {isUploading ? "Uploading..." : "Upload"}
          </button>
        )}

        {isUploading && (
          <button
            onClick={cancelUpload}
            className="file-uploader__cancel-button"
            aria-label="Cancel upload"
            tabIndex={0}
          >
            Cancel
          </button>
        )}
      </div>

      {isUploading && (
        <div className="file-uploader__progress">
          <div
            className="file-uploader__progress-bar"
            style={{ width: `${progress}%` }}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-valuenow={progress}
            role="progressbar"
          />
          <span className="file-uploader__progress-text">
            {progress.toFixed(0)}%
          </span>
        </div>
      )}

      {errors.length > 0 && (
        <div className="file-uploader__errors">
          <h4>Errors:</h4>
          <ul className="file-uploader__error-list">
            {errors.map((error, index) => (
              <li key={index} className="file-uploader__error-item">
                <strong>{error.fileName}</strong>: {error.message}
              </li>
            ))}
          </ul>
        </div>
      )}

      {showFileList && selectedFiles.length > 0 && (
        <div className="file-uploader__selected-files">
          <h4>Selected Files:</h4>
          <ul className="file-uploader__file-list">
            {selectedFiles.map((file, index) => (
              <li key={index} className="file-uploader__file-item">
                <span className="file-uploader__file-name">{file.name}</span>
                <span className="file-uploader__file-size">
                  ({formatFileSize(file.size)})
                </span>
                <button
                  onClick={() => handleRemoveFile(index)}
                  className="file-uploader__file-remove"
                  disabled={isUploading}
                  aria-label={`Remove ${file.name}`}
                  tabIndex={0}
                >
                  ✕
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}

      {showFileList && uploadedFiles.length > 0 && (
        <div className="file-uploader__uploaded-files">
          <h4>Uploaded Files:</h4>
          <ul className="file-uploader__file-list">
            {uploadedFiles.map((file, index) => (
              <li
                key={index}
                className="file-uploader__file-item file-uploader__file-item--uploaded"
              >
                <span className="file-uploader__file-name">
                  {file.displayName || file.name}
                </span>
                <span className="file-uploader__file-size">
                  ({formatFileSize(file.fileSize)})
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
