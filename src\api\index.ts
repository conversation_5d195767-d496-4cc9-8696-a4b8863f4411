import { ApiClient } from './core/apiClient';
import { FileService } from './services/fileService';
import { FolderService } from './services/folderService';
import { SyncService } from './services/syncService';
import { ChunkedUploadService } from './services/chunkedUploadService';

/**
 * Main API client for VeasyFileManager
 */
export class VeasyFileManagerAPI {
  private apiClient: ApiClient;
  
  readonly files: FileService;
  readonly folders: FolderService;
  readonly sync: SyncService;
  readonly chunkedUpload: ChunkedUploadService;
  
  /**
   * Create a new API client instance
   * @param baseURL Base URL for the API (default: '/api/v1')
   * @param token Optional JWT Bearer token for authentication
   */
  constructor(baseURL: string = '/api/v1', token?: string) {
    this.apiClient = new ApiClient(baseURL, token);
    
    // Initialize services
    this.files = new FileService(this.apiClient);
    this.folders = new FolderService(this.apiClient);
    this.sync = new SyncService(this.apiClient);
    this.chunkedUpload = new ChunkedUploadService(this.apiClient);
  }
  
  /**
   * Update the authentication token
   * @param token New JWT Bearer token
   */
  setToken(token: string): void {
    this.apiClient.setToken(token);
  }
  
  /**
   * Check if the API is reachable
   * @returns True if the API is reachable
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.apiClient.get<{ status: string }>('/health-check');
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Re-export interfaces
export * from './types/interfaces';

// Default export
export default VeasyFileManagerAPI; 