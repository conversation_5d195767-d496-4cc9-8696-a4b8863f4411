"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { UserManager, UserManagerSettings, Log } from "oidc-client-ts";
import { oidcConfig } from "@/lib/oidcConfig";
import { DocumentIcon } from "@heroicons/react/24/outline";

// Bật log chi tiết trong môi trường phát triển
if (process.env.NODE_ENV === "development") {
  Log.setLogger(console);
  Log.setLevel(Log.DEBUG);
}

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<"processing" | "success" | "error">(
    "processing"
  );
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log("Auth callback started");

        // L<PERSON>y URL hiện tại để đảm bảo có đầy đủ query params
        const currentUrl = window.location.href;
        console.log("Current URL:", currentUrl);

        // Kiểm tra xem có state trong URL không
        const hasState = searchParams?.has("state");
        console.log("Has state in URL:", hasState);

        const userManager = new UserManager(oidcConfig as UserManagerSettings);

        // Xử lý callback với URL đầy đủ
        const user = await userManager.signinCallback(currentUrl);
        console.log("Auth callback completed", user);

        if (user && !user.expired) {
          setStatus("success");

          // Lấy returnUrl từ user.state hoặc từ session storage
          let returnUrl = "/";
          if (user.state) {
            returnUrl = typeof user.state === "string" ? user.state : "/";
          } else {
            const storedUrl = sessionStorage.getItem("auth_return_url");
            if (storedUrl) {
              returnUrl = storedUrl;
              sessionStorage.removeItem("auth_return_url");
            }
          }

          console.log("Redirecting to:", returnUrl);
          setTimeout(() => {
            router.push(returnUrl);
          }, 1500);
        } else {
          setStatus("error");
          setError("Invalid authentication response");
        }
      } catch (err) {
        console.error("Callback error:", err);
        setStatus("error");
        setError(err instanceof Error ? err.message : "Authentication failed");
      }
    };

    handleCallback();
  }, [router, searchParams]);

  const handleRetry = () => {
    router.push("/auth/login");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center">
            <DocumentIcon className="h-12 w-12 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            PDF OCR Dashboard
          </h2>
        </div>

        <div className="mt-8 space-y-6">
          {status === "processing" && (
            <div className="text-center">
              <div className="mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
              <p className="text-gray-600">Processing authentication...</p>
            </div>
          )}

          {status === "success" && (
            <div className="text-center">
              <div className="mb-4">
                <div className="rounded-full h-8 w-8 bg-green-100 flex items-center justify-center mx-auto">
                  <svg
                    className="h-5 w-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-gray-600">
                Authentication successful! Redirecting...
              </p>
            </div>
          )}

          {status === "error" && (
            <div className="text-center">
              <div className="mb-4">
                <div className="rounded-full h-8 w-8 bg-red-100 flex items-center justify-center mx-auto">
                  <svg
                    className="h-5 w-5 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-red-600 mb-4">Authentication failed</p>
              {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
              <button
                onClick={handleRetry}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try again
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
