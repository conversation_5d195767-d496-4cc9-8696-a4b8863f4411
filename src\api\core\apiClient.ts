import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiErrorResponse } from '../types/interfaces';

export class ApiClient {
  private client: AxiosInstance;
  
  constructor(baseURL: string = '/api/v1', token?: string) {
    this.client = axios.create({
      baseURL,
      headers: token ? { Authorization: `Bearer ${token}` } : {}
    });
    
    this.setupInterceptors();
  }
  
  // Helper to update token if needed after initialization
  setToken(token: string): void {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }
  
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // You could add request logging or transformations here
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    
    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error: AxiosError<ApiErrorResponse>) => {
        const errorResponse = error.response?.data;
        
        // You can customize error handling here
        console.error('API Error:', {
          url: error.config?.url,
          status: error.response?.status,
          error: errorResponse
        });
        
        return Promise.reject(error);
      }
    );
  }
  
  // Helper methods to wrap axios with better typing
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, config);
    return response.data;
  }
  
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }
  
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config);
    return response.data;
  }
  
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);
    return response.data;
  }
  
  // Helper for downloading files
  async downloadFile(url: string, config?: AxiosRequestConfig): Promise<Blob> {
    const response = await this.client.get<Blob>(url, {
      ...config,
      responseType: 'blob'
    });
    return response.data;
  }
  
  // Get the underlying axios instance for special cases
  getAxiosInstance(): AxiosInstance {
    return this.client;
  }
} 