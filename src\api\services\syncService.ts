import { ApiClient } from '../core/apiClient';
import { SyncOptions, SyncStatusResponse, SyncJobDto } from '../types/interfaces';

export class SyncService {
  private client: ApiClient;
  
  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }
  
  /**
   * Trigger Google Drive sync for a file
   * @param options Sync options
   * @returns Information about the created sync job
   */
  async triggerGoogleDriveSync(options?: SyncOptions): Promise<{ jobId: string; status: string; message: string }> {
    return this.client.post<{ jobId: string; status: string; message: string }>(
      '/sync/google-drive',
      options || {}
    );
  }
  
  /**
   * Get sync status for files
   * @param fileId Optional file ID to filter status by specific file
   * @param provider Storage provider (default: 'GoogleDrive')
   * @returns Sync status information
   */
  async getSyncStatus(fileId?: string, provider: string = 'GoogleDrive'): Promise<SyncStatusResponse> {
    const params: Record<string, any> = { provider };
    if (fileId) params.fileId = fileId;
    
    return this.client.get<SyncStatusResponse>('/sync/status', { params });
  }
  
  /**
   * Cancel a sync job
   * @param jobId ID of the sync job to cancel
   * @returns Status of the cancellation
   */
  async cancelSync(jobId: string): Promise<{ success: boolean; message: string }> {
    return this.client.post<{ success: boolean; message: string }>(
      `/sync/jobs/${jobId}/cancel`
    );
  }
  
  /**
   * Get detailed information about a specific sync job
   * @param jobId ID of the sync job
   * @returns Detailed job information
   */
  async getSyncJobDetails(jobId: string): Promise<SyncJobDto> {
    return this.client.get<SyncJobDto>(`/sync/jobs/${jobId}`);
  }
  
  /**
   * Get sync history for a file
   * @param fileId ID of the file
   * @param limit Maximum number of history entries (default: 10)
   * @returns List of past sync jobs for the file
   */
  async getSyncHistory(fileId: string, limit: number = 10): Promise<SyncJobDto[]> {
    return this.client.get<SyncJobDto[]>(`/sync/history/${fileId}`, {
      params: { limit }
    });
  }
}

