import { WebStorageStateStore } from "oidc-client-ts";
import { AuthProviderProps } from "react-oidc-context";

const OIDC_BASE =
  process.env.NEXT_PUBLIC_OIDC_AUTHORITY || "http://localhost:3000";
const REDIRECT_URI =
  process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI ||
  "http://localhost:3000/auth/callback";
const POST_LOGOUT_REDIRECT_URI =
  process.env.NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI ||
  "http://localhost:3000/logout-callback";
const SILENT_REDIRECT_URI =
  process.env.NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI ||
  "http://localhost:3000/silent-callback";

// Tạo cấu hình OIDC với kiểm tra môi trường
const createOidcConfig = (): AuthProviderProps => {
  const config: AuthProviderProps = {
    authority: OIDC_BASE,
    client_id: process.env.NEXT_PUBLIC_OIDC_CLIENT_ID!,
    redirect_uri: REDIRECT_URI,
    post_logout_redirect_uri: POST_LOGOUT_REDIRECT_URI,
    response_type: "code",
    scope: process.env.NEXT_PUBLIC_OIDC_SCOPE || "openid profile email roles ",
    automaticSilentRenew: true,
    loadUserInfo: true,
    monitorSession: true,
    client_secret: process.env.NEXT_PUBLIC_OIDC_CLIENT_SECRET || "secret",
    metadataUrl: `${OIDC_BASE}/.well-known/openid-configuration`,
    response_mode: "query", // Thay đổi từ 'fragment' sang 'query'
    silent_redirect_uri: SILENT_REDIRECT_URI,
    revokeTokensOnSignout: true,
    onSigninCallback: () => {
      if (typeof window !== "undefined") {
        window.history.replaceState(
          {},
          document.title,
          window.location.pathname
        );
      }
    },
  };

  // Chỉ thêm stateStore và userStore khi đang chạy ở client-side
  if (typeof window !== "undefined") {
    config.stateStore = new WebStorageStateStore({
      store: window.localStorage,
    });
    config.userStore = new WebStorageStateStore({ store: window.localStorage });
  }

  return config;
};

export const oidcConfig = createOidcConfig();
