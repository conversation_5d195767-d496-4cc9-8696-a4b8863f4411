# Frontend Developer Guide - Chunked Upload Implementation

## Overview

This guide shows how to implement chunked upload on the frontend to work with the VeasyFileManager API. Chunked upload splits large files into smaller pieces for more reliable uploads.

## Benefits

- ✅ Upload large files (>100MB) reliably
- ✅ Resume uploads if interrupted  
- ✅ Show accurate progress
- ✅ Avoid timeouts on slow connections
- ✅ Parallel chunk uploads for speed

## API Flow

```
1. Initialize Upload Session
   POST /files/upload/chunked/init
   
2. Upload Chunks (repeat for each chunk)
   POST /files/upload/chunked/{sessionId}/chunk
   
3. Complete Upload
   POST /files/upload/chunked/{sessionId}/complete
```

## JavaScript Implementation

### Basic ChunkedUploader Class

```javascript
class ChunkedUploader {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || '/api/v1';
        this.chunkSize = options.chunkSize || 10 * 1024 * 1024; // 10MB
        this.authToken = options.authToken;
        this.onProgress = options.onProgress || (() => {});
        this.onComplete = options.onComplete || (() => {});
        this.onError = options.onError || (() => {});
    }

    async uploadFile(file, metadata = {}) {
        try {
            // Step 1: Initialize session
            const session = await this.initializeSession(file, metadata);
            
            // Step 2: Upload chunks
            await this.uploadChunks(file, session);
            
            // Step 3: Complete upload
            const result = await this.completeUpload(session);
            
            this.onComplete(result);
            return result;
        } catch (error) {
            this.onError(error);
            throw error;
        }
    }

    async initializeSession(file, metadata) {
        const payload = {
            fileName: file.name,
            totalFileSize: file.size,
            contentType: file.type,
            parentFolderId: metadata.parentFolderId,
            displayName: metadata.displayName || file.name,
            description: metadata.description,
            syncToGoogleDrive: metadata.syncToGoogleDrive ?? true,
            tags: metadata.tags || []
        };

        const response = await fetch(`${this.baseUrl}/files/upload/chunked/init`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.authToken}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`Failed to initialize: ${response.statusText}`);
        }

        return await response.json();
    }

    async uploadChunks(file, session) {
        const totalChunks = Math.ceil(file.size / session.chunkSize);
        
        for (let i = 0; i < totalChunks; i++) {
            const start = i * session.chunkSize;
            const end = Math.min(start + session.chunkSize, file.size);
            const chunk = file.slice(start, end);
            
            await this.uploadSingleChunk(chunk, i + 1, session);
            
            // Update progress
            const progress = ((i + 1) / totalChunks) * 100;
            this.onProgress({
                progress,
                chunkNumber: i + 1,
                totalChunks,
                uploadedBytes: end,
                totalBytes: file.size
            });
        }
    }

    async uploadSingleChunk(chunk, chunkNumber, session) {
        const formData = new FormData();
        formData.append('chunk', chunk);
        formData.append('chunkNumber', chunkNumber.toString());

        const response = await fetch(
            `${this.baseUrl}/files/upload/chunked/${session.sessionId}/chunk`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: formData
            }
        );

        if (!response.ok) {
            throw new Error(`Failed to upload chunk ${chunkNumber}`);
        }

        return await response.json();
    }

    async completeUpload(session) {
        const response = await fetch(
            `${this.baseUrl}/files/upload/chunked/${session.sessionId}/complete`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: JSON.stringify({
                    chunkHashes: [], // Optional for basic implementation
                    finalFileHash: null
                })
            }
        );

        if (!response.ok) {
            throw new Error('Failed to complete upload');
        }

        return await response.json();
    }
}
```

## React Hook Example

```javascript
import { useState, useCallback } from 'react';

export const useChunkedUpload = (options = {}) => {
    const [isUploading, setIsUploading] = useState(false);
    const [progress, setProgress] = useState(0);
    const [error, setError] = useState(null);
    const [result, setResult] = useState(null);

    const uploadFile = useCallback(async (file, metadata = {}) => {
        try {
            setIsUploading(true);
            setError(null);
            setProgress(0);
            
            const uploader = new ChunkedUploader({
                ...options,
                onProgress: (progressData) => {
                    setProgress(progressData.progress);
                },
                onComplete: (result) => {
                    setResult(result);
                    setIsUploading(false);
                },
                onError: (error) => {
                    setError(error);
                    setIsUploading(false);
                }
            });
            
            await uploader.uploadFile(file, metadata);
        } catch (error) {
            setError(error);
            setIsUploading(false);
        }
    }, [options]);

    const reset = useCallback(() => {
        setIsUploading(false);
        setProgress(0);
        setError(null);
        setResult(null);
    }, []);

    return {
        uploadFile,
        reset,
        isUploading,
        progress,
        error,
        result
    };
};
```

## React Component Example

```jsx
import React, { useState } from 'react';
import { useChunkedUpload } from './useChunkedUpload';

const FileUploadComponent = () => {
    const [selectedFile, setSelectedFile] = useState(null);
    
    const {
        uploadFile,
        reset,
        isUploading,
        progress,
        error,
        result
    } = useChunkedUpload({
        baseUrl: '/api/v1',
        authToken: localStorage.getItem('authToken'),
        chunkSize: 5 * 1024 * 1024 // 5MB chunks
    });

    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        setSelectedFile(file);
        reset();
    };

    const handleUpload = () => {
        if (selectedFile) {
            uploadFile(selectedFile, {
                displayName: selectedFile.name,
                description: 'Uploaded via chunked upload'
            });
        }
    };

    return (
        <div>
            <h3>Chunked File Upload</h3>
            
            <input
                type="file"
                onChange={handleFileSelect}
                disabled={isUploading}
            />
            
            {selectedFile && (
                <div>
                    <p>Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</p>
                    
                    <button 
                        onClick={handleUpload}
                        disabled={isUploading}
                    >
                        {isUploading ? 'Uploading...' : 'Upload'}
                    </button>
                </div>
            )}
            
            {isUploading && (
                <div>
                    <div style={{
                        width: '100%',
                        height: '20px',
                        backgroundColor: '#f0f0f0',
                        borderRadius: '10px',
                        overflow: 'hidden'
                    }}>
                        <div style={{
                            width: `${progress}%`,
                            height: '100%',
                            backgroundColor: '#4CAF50',
                            transition: 'width 0.3s'
                        }} />
                    </div>
                    <p>{progress.toFixed(1)}%</p>
                </div>
            )}
            
            {error && (
                <div style={{color: 'red'}}>
                    Error: {error.message}
                </div>
            )}
            
            {result && (
                <div style={{color: 'green'}}>
                    Upload successful! File ID: {result.id}
                </div>
            )}
        </div>
    );
};

export default FileUploadComponent;
```

## Advanced Features

### With Retry Logic

```javascript
class AdvancedChunkedUploader extends ChunkedUploader {
    constructor(options) {
        super(options);
        this.maxRetries = options.maxRetries || 3;
    }

    async uploadSingleChunk(chunk, chunkNumber, session, retryCount = 0) {
        try {
            return await super.uploadSingleChunk(chunk, chunkNumber, session);
        } catch (error) {
            if (retryCount < this.maxRetries) {
                console.log(`Retrying chunk ${chunkNumber}, attempt ${retryCount + 1}`);
                await this.delay(Math.pow(2, retryCount) * 1000); // Exponential backoff
                return this.uploadSingleChunk(chunk, chunkNumber, session, retryCount + 1);
            }
            throw error;
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

### Parallel Upload

```javascript
class ParallelChunkedUploader extends ChunkedUploader {
    constructor(options) {
        super(options);
        this.parallelUploads = options.parallelUploads || 3;
    }

    async uploadChunks(file, session) {
        const totalChunks = Math.ceil(file.size / session.chunkSize);
        const chunks = [];
        
        // Create all chunks
        for (let i = 0; i < totalChunks; i++) {
            const start = i * session.chunkSize;
            const end = Math.min(start + session.chunkSize, file.size);
            chunks.push({
                data: file.slice(start, end),
                number: i + 1,
                size: end - start
            });
        }

        // Upload chunks in parallel with concurrency limit
        const results = [];
        for (let i = 0; i < chunks.length; i += this.parallelUploads) {
            const batch = chunks.slice(i, i + this.parallelUploads);
            const batchPromises = batch.map(chunk => 
                this.uploadSingleChunk(chunk.data, chunk.number, session)
            );
            
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            
            // Update progress
            const progress = (results.length / chunks.length) * 100;
            this.onProgress({
                progress,
                uploadedChunks: results.length,
                totalChunks: chunks.length
            });
        }
        
        return results;
    }
}
```

## Error Handling

```javascript
const handleUploadError = (error) => {
    if (error.message.includes('Failed to initialize')) {
        // Session initialization failed
        return 'Unable to start upload. Please check your connection and try again.';
    }
    
    if (error.message.includes('Failed to upload chunk')) {
        // Chunk upload failed
        return 'Upload interrupted. Please try again.';
    }
    
    if (error.message.includes('Failed to complete')) {
        // Completion failed
        return 'Upload nearly complete but failed to finalize. Please contact support.';
    }
    
    if (error.message.includes('413') || error.message.includes('too large')) {
        // File too large
        return 'File is too large. Please choose a smaller file.';
    }
    
    return 'Upload failed. Please try again.';
};
```

## Testing

```javascript
// Test with different file sizes
const testFiles = [
    new File(['small'], 'small.txt', { type: 'text/plain' }),
    new File([new ArrayBuffer(50 * 1024 * 1024)], 'medium.bin'), // 50MB
    new File([new ArrayBuffer(500 * 1024 * 1024)], 'large.bin')  // 500MB
];

testFiles.forEach(async (file) => {
    console.log(`Testing upload for ${file.name} (${file.size} bytes)`);
    
    const uploader = new ChunkedUploader({
        baseUrl: '/api/v1',
        authToken: 'test-token',
        onProgress: (progress) => {
            console.log(`${file.name}: ${progress.progress.toFixed(1)}%`);
        }
    });
    
    try {
        const result = await uploader.uploadFile(file);
        console.log(`✅ ${file.name} uploaded successfully:`, result.id);
    } catch (error) {
        console.error(`❌ ${file.name} upload failed:`, error.message);
    }
});
```

## Best Practices

1. **Chunk Size**: Use 5-10MB chunks for most cases
2. **Progress Updates**: Update UI smoothly but not too frequently
3. **Error Handling**: Implement retry logic with exponential backoff
4. **Memory Management**: Release chunk references after upload
5. **User Experience**: Show clear progress and allow cancellation
6. **Network Optimization**: Adjust parallel uploads based on connection speed
7. **Validation**: Validate file type and size before starting upload
8. **Security**: Always validate auth tokens and handle 401/403 errors

## Production Checklist

- [ ] Add file type validation
- [ ] Implement upload cancellation
- [ ] Add resume capability for interrupted uploads
- [ ] Configure optimal chunk size for your users
- [ ] Add comprehensive error handling
- [ ] Implement progress persistence
- [ ] Add upload analytics/monitoring
- [ ] Test with different network conditions
- [ ] Add rate limiting protection
- [ ] Implement proper cleanup on errors 